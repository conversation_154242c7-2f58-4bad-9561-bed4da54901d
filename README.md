# AI安全网关 (AI Gateway)

一个为OpenAI API提供安全检测和内容过滤的智能网关服务，支持多模型自动路由和灵活的安全策略配置。

## 🚀 核心功能

### 1. 安全内容检测
- **易盾文本检测**: 基于网易易盾反垃圾云服务，提供专业的内容安全检测
- **实时检测**: 支持对用户输入和AI输出进行实时安全检测
- **分块检测**: 智能分割长文本，确保检测准确性
- **流式检测**: 支持对流式输出进行逐块安全检测

### 2. 多模型自动路由
- **模型映射**: 通过配置文件自动路由到不同的AI服务提供商
- **安全模式**: 支持模型名称后缀`:safe`启用安全检测
- **API密钥管理**: 为不同模型配置独立的API密钥和端点
- **负载均衡**: 智能选择可用的模型服务

### 3. 灵活的回退策略
- **替换策略** (`replace`): 用安全内容替换违规内容
- **阻止策略** (`block`): 完全阻止违规内容输出
- **继续策略** (`continue`): 保持原内容但记录安全信息

### 4. OpenAI兼容API
- **完全兼容**: 支持标准的OpenAI API格式
- **流式输出**: 支持Server-Sent Events流式响应
- **模型列表**: 提供可用模型的动态列表
- **错误处理**: 标准化的错误响应格式

## 📋 环境要求

- Python 3.13+
- 易盾账号和API密钥
- 支持的AI模型API密钥（如OpenAI、阿里云等）

## ⚙️ 环境配置

创建 `.env` 文件并配置以下环境变量：

```bash
# 易盾配置（必需）
YIDUN_SECRET_ID=your_yidun_secret_id
YIDUN_SECRET_KEY=your_yidun_secret_key
YIDUN_BUSINESS_ID=your_yidun_business_id

# OpenAI配置（可选，可通过模型映射配置）
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
DEBUG=false

# 安全配置
ENABLE_REALTIME_CHECK=true
ENABLE_CHUNK_CHECK=true
ENABLE_STREAM_CHECK=false
FALLBACK_STRATEGY=replace
MAX_TOKEN_CHUNK=128
MAX_DETECT_CHUNK=200
```

## 🔧 模型配置

编辑 `map.json` 文件配置支持的模型：

```json
{
    "qwen-plus-latest": {
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "api_key": "your_qwen_api_key"
    },
    "gpt-3.5-turbo": {
        "base_url": "https://api.openai.com/v1",
        "api_key": "your_openai_api_key"
    },
    "claude-3-sonnet": {
        "base_url": "https://api.anthropic.com/v1",
        "api_key": "your_anthropic_api_key"
    }
}
```

每个模型都支持两种模式：
- `model-name`: 基础模式（无安全检测）
- `model-name:safe`: 安全模式（启用安全检测）

## 🚀 安装和运行

### 1. 安装依赖

```bash
# 使用 uv（推荐）
uv sync

# 或使用 pip
pip install -r requirements.txt
```

### 2. 启动服务

```bash
# 直接运行
python main.py

# 或使用 uv
uv run python main.py
```

服务启动后将在 `http://localhost:8080` 提供API服务。

## 📡 API接口

### 1. 服务状态

#### GET `/`
获取服务基本信息和可用模型列表

```bash
curl http://localhost:8080/
```

#### GET `/health`
健康检查，返回各组件状态

```bash
curl http://localhost:8080/health
```

### 2. 聊天完成API

#### POST `/v1/chat/completions`
兼容OpenAI格式的聊天完成接口

```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "qwen-plus-latest:safe",
    "messages": [
      {"role": "user", "content": "你好，请介绍一下自己"}
    ],
    "stream": true
  }'
```

### 3. 模型列表

#### GET `/v1/models`
获取可用模型列表（OpenAI兼容格式）

```bash
curl http://localhost:8080/v1/models
```

### 4. 安全检测

#### POST `/v1/security/check`
独立的内容安全检测接口

```bash
curl -X POST http://localhost:8080/v1/security/check \
  -H "Content-Type: application/json" \
  -d '{
    "content": "需要检测的文本内容"
  }'
```

## 🛡️ 安全策略配置

### 回退策略

1. **替换策略** (`replace`): 默认策略，用安全内容替换违规内容
2. **阻止策略** (`block`): 完全阻止违规内容输出，返回错误
3. **继续策略** (`continue`): 保持原内容，但在响应中标记安全信息

### 检测配置

- `enable_realtime_check`: 是否对用户输入进行实时检测
- `enable_chunk_check`: 是否对长文本进行分块检测
- `enable_stream_check`: 是否对流式输出进行逐块检测
- `max_token_chunk`: 每个检测块的最大token数
- `max_detect_chunk`: 每次检测的最大字符数

## 📊 使用示例

### Python客户端示例

```python
import openai

# 配置客户端指向网关
client = openai.OpenAI(
    api_key="your-api-key",
    base_url="http://localhost:8080/v1"
)

# 使用安全模式的模型
response = client.chat.completions.create(
    model="qwen-plus-latest:safe",
    messages=[
        {"role": "user", "content": "请写一个Python函数"}
    ],
    stream=True
)

for chunk in response:
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="")
```

### cURL示例

```bash
# 流式请求
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "qwen-plus-latest:safe",
    "messages": [
      {"role": "user", "content": "解释一下机器学习"}
    ],
    "stream": true,
    "temperature": 0.7,
    "max_tokens": 1000
  }'
```

## 🔍 监控和日志

### 日志配置
服务使用结构化日志记录，包含以下信息：
- 请求处理时间
- 安全检测结果
- 模型路由信息
- 错误和异常信息

### 健康检查
通过 `/health` 端点监控服务状态：
- 文本检测器状态
- 模型映射器状态
- 安全过滤器状态
- 配置验证状态

## 🔧 高级配置

### 自定义安全规则
可以通过修改 `gateway/fallback.py` 中的替换规则来自定义安全处理逻辑。

### 扩展模型支持
在 `map.json` 中添加新的模型配置即可支持更多AI服务提供商。

### 性能优化
- 启用检测结果缓存
- 调整token分块大小
- 配置合适的超时时间

## 📝 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📞 支持

如有问题，请通过以下方式联系：
- 提交GitHub Issue
- 查看项目文档
- 联系项目维护者
